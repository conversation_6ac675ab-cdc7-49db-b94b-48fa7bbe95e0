import React from 'react';
import VoiceActivityCircle from './components/voice-activity-circle';

// Simple test component to verify the VoiceActivityCircle works
export default function TestComponent() {
  return (
    <div style={{ padding: '2rem', textAlign: 'center' }}>
      <h1>Voice Activity Circle Test</h1>
      
      <div style={{ margin: '2rem 0' }}>
        <h2>Idle State</h2>
        <VoiceActivityCircle size="lg" showLabel={true} />
      </div>
      
      <div style={{ margin: '2rem 0' }}>
        <h2>User Speaking</h2>
        <VoiceActivityCircle 
          size="lg" 
          showLabel={true} 
          isUserSpeaking={true}
        />
      </div>
      
      <div style={{ margin: '2rem 0' }}>
        <h2>AI Speaking</h2>
        <VoiceActivityCircle 
          size="lg" 
          showLabel={true} 
          isAISpeaking={true}
        />
      </div>
      
      <div style={{ margin: '2rem 0' }}>
        <h2>Muted</h2>
        <VoiceActivityCircle 
          size="lg" 
          showLabel={true} 
          isMuted={true}
        />
      </div>
    </div>
  );
}
