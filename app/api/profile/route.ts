import { type NextRequest, NextResponse } from "next/server"
import { getAccessToken } from "@/lib/auth-utils"

export async function GET(request: NextRequest) {
  try {
    // Get auth token from headers
    const authHeader = request.headers.get('authorization')
    let token = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    } else {
      // Fallback to getting token from auth utils
      token = getAccessToken()
    }

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" }, 
        { status: 401 }
      )
    }

    // Make request to the backend server
    const flyServerUrl = process.env.FLY_SERVER_URL || "https://medbot-backend.fly.dev"
    const response = await fetch(`${flyServerUrl}/api/v1/profile`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || "Failed to fetch profile" }, 
        { status: response.status }
      )
    }

    // Return the profile data
    return NextResponse.json(data, { status: 200 })

  } catch (error) {
    console.error("Profile GET API error:", error)
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { error: "Unable to connect to profile service. Please try again later." }, 
        { status: 503 }
      )
    }
    
    return NextResponse.json(
      { error: "Internal server error. Please try again later." }, 
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { first_name, last_name, date_of_birth } = body

    // Get auth token from headers
    const authHeader = request.headers.get('authorization')
    let token = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    } else {
      // Fallback to getting token from auth utils
      token = getAccessToken()
    }

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" }, 
        { status: 401 }
      )
    }

    // Validate input
    if (!first_name && !last_name && !date_of_birth) {
      return NextResponse.json(
        { error: "At least one field must be provided for update" }, 
        { status: 400 }
      )
    }

    // Validate name fields if provided
    if (first_name && first_name.trim().length < 2) {
      return NextResponse.json(
        { error: "First name must be at least 2 characters long" }, 
        { status: 400 }
      )
    }

    if (last_name && last_name.trim().length < 2) {
      return NextResponse.json(
        { error: "Last name must be at least 2 characters long" }, 
        { status: 400 }
      )
    }

    // Validate date of birth format if provided
    if (date_of_birth && !/^\d{4}-\d{2}-\d{2}$/.test(date_of_birth)) {
      return NextResponse.json(
        { error: "Date of birth must be in YYYY-MM-DD format" }, 
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    if (first_name) updateData.first_name = first_name.trim()
    if (last_name) updateData.last_name = last_name.trim()
    if (date_of_birth) updateData.date_of_birth = date_of_birth

    // Make request to the backend server
    const flyServerUrl = process.env.FLY_SERVER_URL || "https://medbot-backend.fly.dev"
    const response = await fetch(`${flyServerUrl}/api/v1/profile`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify(updateData),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || "Failed to update profile" }, 
        { status: response.status }
      )
    }

    // Return the updated profile data
    return NextResponse.json(data, { status: 200 })

  } catch (error) {
    console.error("Profile PUT API error:", error)
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { error: "Unable to connect to profile service. Please try again later." }, 
        { status: 503 }
      )
    }
    
    return NextResponse.json(
      { error: "Internal server error. Please try again later." }, 
      { status: 500 }
    )
  }
}
