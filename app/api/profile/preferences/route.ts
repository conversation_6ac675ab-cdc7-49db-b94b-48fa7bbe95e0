import { type NextRequest, NextResponse } from "next/server"
import { getAccessToken } from "@/lib/auth-utils"

export async function GET(request: NextRequest) {
  try {
    // Get auth token from headers
    const authHeader = request.headers.get('authorization')
    let token = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    } else {
      // Fallback to getting token from auth utils
      token = getAccessToken()
    }

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" }, 
        { status: 401 }
      )
    }

    // Make request to the backend server
    const flyServerUrl = process.env.FLY_SERVER_URL || "https://medbot-backend.fly.dev"
    const response = await fetch(`${flyServerUrl}/api/v1/profile/preferences`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || "Failed to fetch preferences" }, 
        { status: response.status }
      )
    }

    // Return the preferences data
    return NextResponse.json(data, { status: 200 })

  } catch (error) {
    console.error("Preferences GET API error:", error)
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { error: "Unable to connect to preferences service. Please try again later." }, 
        { status: 503 }
      )
    }
    
    return NextResponse.json(
      { error: "Internal server error. Please try again later." }, 
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { theme, notifications_enabled } = body

    // Get auth token from headers
    const authHeader = request.headers.get('authorization')
    let token = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    } else {
      // Fallback to getting token from auth utils
      token = getAccessToken()
    }

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" }, 
        { status: 401 }
      )
    }

    // Validate input
    if (theme === undefined && notifications_enabled === undefined) {
      return NextResponse.json(
        { error: "At least one preference field must be provided for update" }, 
        { status: 400 }
      )
    }

    // Validate theme value if provided
    if (theme !== undefined && !["light", "dark"].includes(theme)) {
      return NextResponse.json(
        { error: "Theme must be either 'light' or 'dark'" }, 
        { status: 400 }
      )
    }

    // Validate notifications_enabled value if provided
    if (notifications_enabled !== undefined && typeof notifications_enabled !== "boolean") {
      return NextResponse.json(
        { error: "notifications_enabled must be a boolean value" }, 
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    if (theme !== undefined) updateData.theme = theme
    if (notifications_enabled !== undefined) updateData.notifications_enabled = notifications_enabled

    // Make request to the backend server
    const flyServerUrl = process.env.FLY_SERVER_URL || "https://medbot-backend.fly.dev"
    const response = await fetch(`${flyServerUrl}/api/v1/profile/preferences`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify(updateData),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || "Failed to update preferences" }, 
        { status: response.status }
      )
    }

    // Return the updated preferences data
    return NextResponse.json(data, { status: 200 })

  } catch (error) {
    console.error("Preferences PUT API error:", error)
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { error: "Unable to connect to preferences service. Please try again later." }, 
        { status: 503 }
      )
    }
    
    return NextResponse.json(
      { error: "Internal server error. Please try again later." }, 
      { status: 500 }
    )
  }
}
