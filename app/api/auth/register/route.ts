import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { email, password, first_name, last_name } = await request.json()

    // Validate input
    if (!email || !password || !first_name || !last_name) {
      return NextResponse.json(
        { error: "All fields are required: email, password, first_name, last_name" }, 
        { status: 400 }
      )
    }

    // Validate email format
    if (!/\S+@\S+\.\S+/.test(email)) {
      return NextResponse.json(
        { error: "Please enter a valid email address" }, 
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { error: "Password must be at least 8 characters long" }, 
        { status: 400 }
      )
    }

    // Validate name fields
    if (first_name.trim().length < 2 || last_name.trim().length < 2) {
      return NextResponse.json(
        { error: "First name and last name must be at least 2 characters long" }, 
        { status: 400 }
      )
    }

    // Make request to the backend server
    const flyServerUrl = process.env.FLY_SERVER_URL || "https://medbot-backend.fly.dev"
    const response = await fetch(`${flyServerUrl}/api/v1/auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: email.trim().toLowerCase(),
        password,
        first_name: first_name.trim(),
        last_name: last_name.trim(),
      }),
    })

    const data = await response.json()

    if (!response.ok) {
      // Handle specific error cases
      if (response.status === 409) {
        return NextResponse.json(
          { error: "An account with this email already exists" }, 
          { status: 409 }
        )
      } else if (response.status === 422) {
        return NextResponse.json(
          { error: data.message || "Invalid input data" }, 
          { status: 422 }
        )
      } else {
        return NextResponse.json(
          { error: data.message || "Registration failed" }, 
          { status: response.status }
        )
      }
    }

    // Return success response with user data (excluding sensitive information)
    return NextResponse.json({
      success: true,
      user: {
        id: data.id,
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name,
        created_at: data.created_at
      },
      message: "Registration successful",
    }, { status: 201 })

  } catch (error) {
    console.error("Registration API error:", error)
    
    // Handle network errors or other unexpected errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        { error: "Unable to connect to registration service. Please try again later." }, 
        { status: 503 }
      )
    }
    
    return NextResponse.json(
      { error: "Internal server error. Please try again later." }, 
      { status: 500 }
    )
  }
}
