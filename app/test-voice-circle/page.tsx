"use client"

import { useState, useEffect } from 'react'
import VoiceActivityCircle from '@/components/voice-activity-circle'
import { Button } from '@/components/ui/button'

export default function TestVoiceCirclePage() {
  const [isUserSpeaking, setIsUserSpeaking] = useState(false)
  const [isAISpeaking, setIsAISpeaking] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [autoDemo, setAutoDemo] = useState(false)

  // Auto demo cycle
  useEffect(() => {
    if (!autoDemo) return

    const interval = setInterval(() => {
      // Reset all states
      setIsUserSpeaking(false)
      setIsAISpeaking(false)
      setIsMuted(false)

      // Cycle through states
      setTimeout(() => setIsUserSpeaking(true), 500)
      setTimeout(() => {
        setIsUserSpeaking(false)
        setIsAISpeaking(true)
      }, 2000)
      setTimeout(() => {
        setIsAISpeaking(false)
        setIsMuted(true)
      }, 3500)
      setTimeout(() => setIsMuted(false), 5000)
    }, 6000)

    return () => clearInterval(interval)
  }, [autoDemo])

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Voice Activity Circle Test</h1>
        
        {/* Main Demo */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="flex justify-center mb-8">
            <VoiceActivityCircle
              size="xl"
              showLabel={true}
              isUserSpeaking={isUserSpeaking}
              isAISpeaking={isAISpeaking}
              isMuted={isMuted}
              className="transition-all duration-300"
            />
          </div>
          
          {/* Controls */}
          <div className="flex flex-wrap justify-center gap-4 mb-6">
            <Button
              onClick={() => {
                setIsUserSpeaking(!isUserSpeaking)
                setIsAISpeaking(false)
              }}
              variant={isUserSpeaking ? "default" : "outline"}
            >
              User Speaking
            </Button>
            <Button
              onClick={() => {
                setIsAISpeaking(!isAISpeaking)
                setIsUserSpeaking(false)
              }}
              variant={isAISpeaking ? "default" : "outline"}
            >
              AI Speaking
            </Button>
            <Button
              onClick={() => setIsMuted(!isMuted)}
              variant={isMuted ? "destructive" : "outline"}
            >
              Muted
            </Button>
            <Button
              onClick={() => {
                setIsUserSpeaking(false)
                setIsAISpeaking(false)
                setIsMuted(false)
              }}
              variant="secondary"
            >
              Reset
            </Button>
          </div>

          <div className="text-center">
            <Button
              onClick={() => setAutoDemo(!autoDemo)}
              variant={autoDemo ? "destructive" : "default"}
            >
              {autoDemo ? "Stop Auto Demo" : "Start Auto Demo"}
            </Button>
          </div>
        </div>

        {/* Size Variations */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-xl font-semibold mb-6 text-center">Size Variations</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">Small</h3>
              <VoiceActivityCircle size="sm" showLabel={false} isUserSpeaking={true} />
            </div>
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">Medium</h3>
              <VoiceActivityCircle size="md" showLabel={false} isAISpeaking={true} />
            </div>
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">Large</h3>
              <VoiceActivityCircle size="lg" showLabel={false} isMuted={true} />
            </div>
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">Extra Large</h3>
              <VoiceActivityCircle size="xl" showLabel={false} />
            </div>
          </div>
        </div>

        {/* State Examples */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-xl font-semibold mb-6 text-center">All States</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">Idle</h3>
              <VoiceActivityCircle size="lg" showLabel={true} />
            </div>
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">User Speaking</h3>
              <VoiceActivityCircle size="lg" showLabel={true} isUserSpeaking={true} />
            </div>
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">AI Speaking</h3>
              <VoiceActivityCircle size="lg" showLabel={true} isAISpeaking={true} />
            </div>
            <div className="text-center">
              <h3 className="text-sm font-medium mb-4">Muted</h3>
              <VoiceActivityCircle size="lg" showLabel={true} isMuted={true} />
            </div>
          </div>
        </div>

        {/* Current State Info */}
        <div className="bg-gray-100 rounded-lg p-4 mt-8">
          <h3 className="font-medium mb-2">Current State:</h3>
          <div className="text-sm text-gray-600">
            <p>User Speaking: {isUserSpeaking ? 'Yes' : 'No'}</p>
            <p>AI Speaking: {isAISpeaking ? 'Yes' : 'No'}</p>
            <p>Muted: {isMuted ? 'Yes' : 'No'}</p>
            <p>Auto Demo: {autoDemo ? 'Running' : 'Stopped'}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
