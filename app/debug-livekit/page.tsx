"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { VoiceSessionAPI } from '@/lib/voice-session-api'
import { isAuthenticated } from '@/lib/auth-utils'

export default function DebugLiveKitPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  const clearLogs = () => {
    setLogs([])
  }

  const testEnvironmentVariables = () => {
    addLog('🔍 Testing Environment Variables...')
    addLog(`NEXT_PUBLIC_LIVEKIT_URL: ${process.env.NEXT_PUBLIC_LIVEKIT_URL || 'NOT SET'}`)
    addLog(`NEXT_PUBLIC_BACKEND_URL: ${process.env.NEXT_PUBLIC_BACKEND_URL || 'NOT SET'}`)
    addLog(`User authenticated: ${isAuthenticated()}`)
  }

  const testVoiceSessionCreation = async () => {
    setIsLoading(true)
    addLog('🔍 Testing Voice Session Creation...')
    
    try {
      // First create a conversation
      addLog('Creating conversation...')
      const conversation = await VoiceSessionAPI.createConversation("Debug Test Conversation")
      addLog(`✅ Conversation created with ID: ${conversation.id}`)

      // Then create voice session
      addLog('Creating voice session...')
      const voiceSession = await VoiceSessionAPI.createVoiceSession({
        conversation_id: conversation.id,
        metadata: {
          instructions: "You are a helpful medical assistant for debugging purposes."
        }
      })
      
      addLog(`✅ Voice session created successfully`)
      addLog(`Token: ${voiceSession.token ? `${voiceSession.token.substring(0, 20)}...` : 'null'}`)
      addLog(`User ID: ${voiceSession.user_id}`)
      addLog(`Session ID: not available in response`)
      
    } catch (error) {
      addLog(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      if (error instanceof Error) {
        addLog(`Stack: ${error.stack}`)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const testLiveKitConnection = async () => {
    setIsLoading(true)
    addLog('🔍 Testing LiveKit Connection...')
    
    try {
      const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://clinical-chatbot-1dewlazs.livekit.cloud'
      addLog(`Attempting to connect to: ${serverUrl}`)
      
      // Test WebSocket connection
      const ws = new WebSocket(serverUrl)
      
      ws.onopen = () => {
        addLog('✅ WebSocket connection opened successfully')
        ws.close()
      }
      
      ws.onerror = (error) => {
        addLog(`❌ WebSocket error: ${error}`)
      }
      
      ws.onclose = (event) => {
        addLog(`🔴 WebSocket closed: Code ${event.code}, Reason: ${event.reason}`)
      }
      
      // Timeout after 10 seconds
      setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          addLog('⏰ WebSocket connection timeout')
          ws.close()
        }
      }, 10000)
      
    } catch (error) {
      addLog(`❌ Connection test error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>LiveKit Debug Console</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button onClick={testEnvironmentVariables} variant="outline">
              Test Environment Variables
            </Button>
            <Button onClick={testVoiceSessionCreation} disabled={isLoading}>
              Test Voice Session Creation
            </Button>
            <Button onClick={testLiveKitConnection} disabled={isLoading}>
              Test LiveKit Connection
            </Button>
            <Button onClick={clearLogs} variant="destructive">
              Clear Logs
            </Button>
          </div>
          
          <div className="border rounded-lg p-4 h-96 overflow-y-auto bg-gray-50 font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-gray-500">Click a test button to see debug output...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
