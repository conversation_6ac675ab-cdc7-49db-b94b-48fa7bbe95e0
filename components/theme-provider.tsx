"use client"

import { useEffect } from "react"

export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize theme on component mount
    const initializeTheme = () => {
      const savedTheme = localStorage.getItem("theme")
      const root = document.documentElement
      
      if (savedTheme === "dark") {
        root.classList.remove("light")
        root.classList.add("dark")
      } else if (savedTheme === "light") {
        root.classList.remove("dark")
        root.classList.add("light")
      } else {
        // Default to system theme
        root.classList.remove("light", "dark")
        const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
        if (systemPrefersDark) {
          root.classList.add("dark")
        } else {
          root.classList.add("light")
        }
      }
    }

    // Initialize theme immediately
    initializeTheme()

    // Listen for system theme changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    const handleSystemThemeChange = () => {
      const savedTheme = localStorage.getItem("theme")
      if (!savedTheme || savedTheme === "system") {
        initializeTheme()
      }
    }

    mediaQuery.addEventListener("change", handleSystemThemeChange)

    return () => {
      mediaQuery.removeEventListener("change", handleSystemThemeChange)
    }
  }, [])

  return <>{children}</>
}
