"use client"

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

import '@/styles/voice-activity.css'

interface VoiceActivityCircleProps {
  isUserSpeaking?: boolean
  isAISpeaking?: boolean
  isMuted?: boolean
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  showLabel?: boolean
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24', 
  lg: 'w-32 h-32',
  xl: 'w-48 h-48'
}

const pulseVariants = {
  idle: {
    scale: 1,
    opacity: 0.8,
  },
  speaking: {
    scale: [1, 1.1, 1],
    opacity: [0.8, 1, 0.8],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  },
  muted: {
    scale: 1,
    opacity: 0.4,
  }
}

const ringVariants = {
  idle: {
    scale: 1,
    opacity: 0,
  },
  speaking: {
    scale: [1, 1.3, 1.5],
    opacity: [0.6, 0.3, 0],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeOut"
    }
  }
}

export default function VoiceActivityCircle({
  isUserSpeaking = false,
  isAISpeaking = false,
  isMuted = false,
  size = 'lg',
  className,
  showLabel = true
}: VoiceActivityCircleProps) {
  const [displayState, setDisplayState] = useState<'idle' | 'user-speaking' | 'ai-speaking' | 'muted'>('idle')

  // Determine display state based on props
  useEffect(() => {
    if (isMuted) {
      setDisplayState('muted')
    } else if (isUserSpeaking) {
      setDisplayState('user-speaking')
    } else if (isAISpeaking) {
      setDisplayState('ai-speaking')
    } else {
      setDisplayState('idle')
    }
  }, [isUserSpeaking, isAISpeaking, isMuted])

  const getCircleColor = () => {
    switch (displayState) {
      case 'user-speaking':
        return 'bg-blue-500'
      case 'ai-speaking':
        return 'bg-blue-600'
      case 'muted':
        return 'bg-blue-300'
      default:
        return 'bg-blue-400'
    }
  }

  const getRingColor = () => {
    switch (displayState) {
      case 'user-speaking':
        return 'border-blue-400'
      case 'ai-speaking':
        return 'border-blue-500'
      default:
        return 'border-blue-300'
    }
  }

  const getLabel = () => {
    switch (displayState) {
      case 'user-speaking':
        return 'You are speaking'
      case 'ai-speaking':
        return 'AI is speaking'
      case 'muted':
        return 'Microphone muted'
      default:
        return 'Ready to speak'
    }
  }

  const isSpeaking = displayState === 'user-speaking' || displayState === 'ai-speaking'

  return (
    <div className={cn("flex flex-col items-center space-y-4", className)}>
      {/* Main circle with rings */}
      <div className={cn("relative flex items-center justify-center", sizeClasses[size])}>
        {/* Outer rings for speaking animation */}
        <AnimatePresence>
          {isSpeaking && (
            <>
              <motion.div
                className={cn(
                  "absolute inset-0 rounded-full border-2",
                  getRingColor()
                )}
                variants={ringVariants}
                initial="idle"
                animate="speaking"
                exit="idle"
              />
              <motion.div
                className={cn(
                  "absolute inset-0 rounded-full border-2",
                  getRingColor()
                )}
                variants={ringVariants}
                initial="idle"
                animate="speaking"
                exit="idle"
                style={{ animationDelay: '0.5s' }}
              />
            </>
          )}
        </AnimatePresence>

        {/* Main circle */}
        <motion.div
          className={cn(
            "relative rounded-full shadow-lg",
            getCircleColor(),
            sizeClasses[size]
          )}
          variants={pulseVariants}
          initial="idle"
          animate={displayState === 'muted' ? 'muted' : isSpeaking ? 'speaking' : 'idle'}
        >
          {/* Inner glow effect */}
          <div className={cn(
            "absolute inset-2 rounded-full bg-white opacity-20",
            displayState === 'muted' && "opacity-10"
          )} />
          
          {/* Center dot */}
          <div className={cn(
            "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
            "w-2 h-2 bg-white rounded-full",
            displayState === 'muted' && "opacity-50"
          )} />
        </motion.div>
      </div>

      {/* Status label */}
      {showLabel && (
        <motion.p
          className={cn(
            "text-sm font-medium text-center",
            displayState === 'user-speaking' && "text-blue-600",
            displayState === 'ai-speaking' && "text-blue-700",
            displayState === 'muted' && "text-gray-500",
            displayState === 'idle' && "text-gray-600"
          )}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {getLabel()}
        </motion.p>
      )}
    </div>
  )
}
