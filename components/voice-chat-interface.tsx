"use client"

import { AnimatePresence, motion } from "framer-motion"
import {
  LiveKitRoom,
  useVoiceAssistant,
  RoomAudioRenderer,
  VoiceAssistantControlBar,
  AgentState,
  DisconnectButton,
  useLocalParticipant,
  useRemoteParticipants,
  useRoomContext,
} from "@livekit/components-react"
import { useCallback, useEffect, useState, useRef } from "react"
import { MediaDeviceFailure, Track, RoomEvent } from "livekit-client"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Phone, Loader2, Shield } from "lucide-react"
import { toast } from "sonner"
import { isAuthenticated } from "@/lib/auth-utils"
import { NoAgentNotification } from "@/components/ui/NoAgentNotification"
import { CloseIcon } from "@/components/ui/CloseIcon"
import VoiceActivityCircle from "@/components/voice-activity-circle"
import { VoiceSessionAPI } from "@/lib/voice-session-api"

interface ConnectionDetails {
  serverUrl: string
  participantToken: string
  participantName: string
  roomName: string
}

interface VoiceChatInterfaceProps {
  conversationId?: string | null
}

interface TranscriptionMessage {
  id: string
  text: string
  role: 'user' | 'assistant'
  timestamp: Date
  isFinal: boolean
  isInterim?: boolean
}

interface LiveKitTranscriptionData {
  text: string
  isFinal: boolean
  isUser: boolean
  participantIdentity: string
  timestamp: Date
  attributes?: Record<string, string>
}

export default function VoiceChatInterface({ conversationId }: VoiceChatInterfaceProps) {
  const [connectionDetails, updateConnectionDetails] = useState<
    ConnectionDetails | undefined
  >(undefined)
  const [agentState, setAgentState] = useState<AgentState>("disconnected")
  const [userAuthenticated, setUserAuthenticated] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)

  // Enhanced transcription state management
  const [transcriptionMessages, setTranscriptionMessages] = useState<TranscriptionMessage[]>([])
  const [interimTranscriptions, setInterimTranscriptions] = useState<Map<string, TranscriptionMessage>>(new Map())
  const [showTranscriptionView, setShowTranscriptionView] = useState(false)
  const [showRealTimeTranscriptions, setShowRealTimeTranscriptions] = useState(true)

  // Legacy state for backward compatibility
  const [voiceSessionMessages, setVoiceSessionMessages] = useState<Array<{
    role: 'user' | 'assistant',
    content: string,
    timestamp: Date
  }>>([])
  const [showTextMessages, setShowTextMessages] = useState(false)
  const transcriptEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new voice session messages are added
  useEffect(() => {
    if (showTextMessages && transcriptEndRef.current) {
      transcriptEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [voiceSessionMessages, showTextMessages])

  // Check authentication status
  useEffect(() => {
    const checkAuth = () => {
      const authenticated = isAuthenticated()
      console.log('Authentication check:', authenticated)
      setUserAuthenticated(authenticated)
      
      // If token exists but is invalid, show a more specific message
      if (!authenticated && localStorage.getItem('access_token')) {
        toast.error('Your session has expired. Please log in again.')
      }
    }

    checkAuth()
    // Check auth status periodically
    const interval = setInterval(checkAuth, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const onConnectButtonClicked = useCallback(async () => {
    console.log('🔵 [DEBUG] Connect button clicked')
    console.log('🔵 [DEBUG] User authenticated:', userAuthenticated)
    console.log('🔵 [DEBUG] Conversation ID:', conversationId)

    if (!userAuthenticated) {
      console.error('❌ [ERROR] User not authenticated')
      toast.error('Please log in to start a voice session')
      return
    }

    try {
      setIsConnecting(true)
      console.log('🔵 [DEBUG] Setting connecting state to true')

      let targetConversationId = conversationId

      // If no conversationId provided, create a new conversation first
      if (!targetConversationId) {
        console.log('🔵 [DEBUG] Creating new conversation...')
        const conversation = await VoiceSessionAPI.createConversation("Voice Consultation")
        targetConversationId = conversation.id
        console.log('✅ [SUCCESS] Created conversation with ID:', targetConversationId)
      }

      // Create voice session using the conversation ID
      console.log('🔵 [DEBUG] Creating voice session for conversation:', targetConversationId)
      const voiceSessionResponse = await VoiceSessionAPI.createVoiceSession({
        conversation_id: targetConversationId,
        metadata: {
          instructions: "You are a helpful medical assistant. Provide clear, accurate medical information while being empathetic and professional."
        }
      })

      console.log('✅ [SUCCESS] Voice session response:', voiceSessionResponse)

      if (!voiceSessionResponse || !voiceSessionResponse.token) {
        console.error('❌ [ERROR] Invalid voice session response')
        throw new Error('Invalid response from voice session API')
      }

      // Make sure to use the correct LiveKit URL from environment variables
      const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://clinical-chatbot-1dewlazs.livekit.cloud';
      console.log('🔵 [DEBUG] LiveKit server URL:', serverUrl)
      console.log('🔵 [DEBUG] Environment NEXT_PUBLIC_LIVEKIT_URL:', process.env.NEXT_PUBLIC_LIVEKIT_URL)

      const connectionDetails = {
        participantToken: voiceSessionResponse.token,
        serverUrl: serverUrl,
        participantName: voiceSessionResponse.user_id,
        roomName: `voice-session-${targetConversationId}`
      }

      console.log('🔵 [DEBUG] Connection details:', {
        ...connectionDetails,
        participantToken: connectionDetails.participantToken ? `${connectionDetails.participantToken.substring(0, 20)}...` : 'null'
      })

      // Update connection details with the voice session response
      updateConnectionDetails(connectionDetails)

      console.log('✅ [SUCCESS] Voice session created successfully')
    } catch (error) {
      console.error('❌ [ERROR] Failed to create voice session:', error)
      toast.error('Failed to connect to voice session: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setIsConnecting(false)
      console.log('🔵 [DEBUG] Setting connecting state to false')
    }
  }, [userAuthenticated, conversationId])

  const onDeviceFailure = useCallback((error?: MediaDeviceFailure) => {
    console.error(error)
    alert(
      "Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab"
    )
  }, [])

  // Add this function to handle session end
  const handleVoiceSessionEnd = useCallback(() => {
    // Reset connection details to end the LiveKit session
    updateConnectionDetails(undefined)
    // Show the text message view with the voice session content
    setShowTextMessages(true)
  }, [])

  // Enhanced transcription handling with comprehensive logging
  const handleLiveKitTranscription = useCallback((
    text: string,
    isFinal: boolean,
    isUser: boolean,
    participantIdentity?: string,
    attributes?: Record<string, string>
  ) => {
    const timestamp = new Date()
    const role = isUser ? 'user' : 'assistant'

    // Comprehensive console logging
    console.log('🎤 [TRANSCRIPTION] Raw LiveKit transcription received:', {
      text,
      isFinal,
      isUser,
      participantIdentity,
      attributes,
      timestamp: timestamp.toISOString(),
      textLength: text.length
    })

    // Create transcription message
    const transcriptionId = `${participantIdentity || role}-${timestamp.getTime()}-${Math.random().toString(36).substring(2, 11)}`
    const transcriptionMessage: TranscriptionMessage = {
      id: transcriptionId,
      text,
      role,
      timestamp,
      isFinal,
      isInterim: !isFinal
    }

    console.log('📝 [TRANSCRIPTION] Processed transcription message:', transcriptionMessage)

    if (isFinal) {
      console.log('✅ [TRANSCRIPTION] Adding final transcription to conversation')

      // Add to final transcriptions
      setTranscriptionMessages(prev => {
        const updated = [...prev, transcriptionMessage]
        console.log('📋 [TRANSCRIPTION] Updated final transcriptions count:', updated.length)
        return updated
      })

      // Remove from interim transcriptions if it exists
      setInterimTranscriptions(prev => {
        const updated = new Map(prev)
        // Remove any interim transcriptions from the same participant
        const keysToRemove = Array.from(updated.keys()).filter(key =>
          key.startsWith(`${participantIdentity || role}-`)
        )
        keysToRemove.forEach(key => updated.delete(key))
        console.log('🗑️ [TRANSCRIPTION] Removed interim transcriptions:', keysToRemove.length)
        return updated
      })

      // Update legacy state for backward compatibility
      setVoiceSessionMessages(prev => {
        const updated = [...prev, {
          role: role as 'user' | 'assistant',
          content: text,
          timestamp
        }]
        console.log('🔄 [TRANSCRIPTION] Updated legacy voice session messages count:', updated.length)
        return updated
      })

      console.log('💾 [TRANSCRIPTION] Final transcription processing complete')
    } else {
      console.log('⏳ [TRANSCRIPTION] Adding interim transcription')

      // Add to interim transcriptions
      setInterimTranscriptions(prev => {
        const updated = new Map(prev)
        updated.set(transcriptionId, transcriptionMessage)
        console.log('📝 [TRANSCRIPTION] Updated interim transcriptions count:', updated.size)
        return updated
      })
    }
  }, [])

  // Legacy function for backward compatibility
  const handleTranscription = useCallback((text: string, role: 'user' | 'assistant') => {
    console.log('🔄 [TRANSCRIPTION] Legacy transcription handler called:', { text, role })
    handleLiveKitTranscription(text, true, role === 'user', role)
  }, [handleLiveKitTranscription])

  if (connectionDetails) {
    console.log('🔵 [DEBUG] Rendering LiveKitRoom with connection details:', {
      ...connectionDetails,
      participantToken: connectionDetails.participantToken ? `${connectionDetails.participantToken.substring(0, 20)}...` : 'null'
    })

    return (
      <main
        data-lk-theme="default"
        className="h-full grid content-center bg-[var(--lk-bg)]"
      >
        <LiveKitRoom
          token={connectionDetails.participantToken}
          serverUrl={connectionDetails.serverUrl}
          connect={connectionDetails !== undefined}
          audio={true}
          video={false}
          onMediaDeviceFailure={onDeviceFailure}
          onConnected={() => {
            console.log('✅ [SUCCESS] LiveKit room connected successfully')
          }}
          onDisconnected={(reason) => {
            console.log('🔴 [INFO] LiveKit room disconnected:', reason)
            updateConnectionDetails(undefined)
          }}
          onError={(error) => {
            console.error('❌ [ERROR] LiveKit room error:', error)
          }}
          className="grid grid-rows-[2fr_1fr] items-center"
        >
          <SimpleVoiceAssistant
            onStateChange={setAgentState}
            onTranscription={handleTranscription}
          />
          <ControlBar
            onConnectButtonClicked={onConnectButtonClicked}
            onEndSession={handleVoiceSessionEnd}
            agentState={agentState}
          />
          <RoomAudioRenderer />
          <NoAgentNotification state={agentState} />
        </LiveKitRoom>
      </main>
    )
  }

  // Add the text message view after voice session ends
  if (showTextMessages && voiceSessionMessages.length > 0) {
    return (
      <div className="h-full flex flex-col bg-gray-50">
        {/* Fixed header */}
        <div className="flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-200 bg-white">
          <h2 className="text-xl font-semibold">Voice Session Transcript</h2>
          <Button
            variant="outline"
            onClick={() => setShowTextMessages(false)}
          >
            New Session
          </Button>
        </div>

        {/* Scrollable messages area */}
        <div className="flex-1 overflow-hidden">
          <div
            className="h-full overflow-y-auto p-4 scroll-smooth chat-scrollbar"
          >
            <div className="space-y-4">
              {voiceSessionMessages.map((msg, idx) => (
                <div
                  key={idx}
                  className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      msg.role === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <p className="whitespace-pre-wrap break-words">{msg.content}</p>
                    <p className={`text-xs mt-1 ${
                      msg.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {msg.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
              <div ref={transcriptEndRef} />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col items-center justify-center px-4 bg-gray-50">
      {/* Authentication Warning */}
      {!userAuthenticated && (
        <Alert className="mb-6 max-w-md border-orange-200 bg-orange-50">
          <Shield className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            You need to be logged in to start a voice session. Please log in first.
          </AlertDescription>
        </Alert>
      )}

      {/* Welcome Message */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">How can I help with your health today?</h2>
        <p className="text-lg text-gray-600 max-w-2xl">
          I'm your AI medical assistant. Start a voice consultation to discuss your symptoms or health concerns.
        </p>
      </div>

      {/* Voice Activity Circle */}
      <div className="relative mb-16">
        <VoiceActivityCircle
          size="xl"
          showLabel={false}
          className="transition-all duration-300"
        />
      </div>

      {/* Control buttons */}
      <div className="flex items-center gap-8 mb-8">
        {/* Start Session button */}
        <Button
          onClick={onConnectButtonClicked}
          disabled={!userAuthenticated || isConnecting || !!connectionDetails}
          size="lg"
          className="w-16 h-16 rounded-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          {isConnecting ? (
            <Loader2 className="h-6 w-6 text-white animate-spin" />
          ) : (
            <Phone className="h-6 w-6 text-white" />
          )}
        </Button>
      </div>

      {/* Status text */}
      <div className="text-center">
        {isConnecting ? (
          <div className="space-y-2">
            <p className="text-lg text-gray-700 font-medium">Connecting to voice session...</p>
            <p className="text-sm text-gray-500">Please wait while we set up your consultation</p>
          </div>
        ) : !userAuthenticated ? (
          <div className="space-y-2">
            <p className="text-lg text-orange-600 font-medium">Please log in first</p>
            <p className="text-sm text-orange-500">You need to be authenticated to start a voice consultation</p>
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-lg text-gray-500">Tap the phone icon to start your voice consultation</p>
            <p className="text-sm text-gray-400">Your conversation is private and secure</p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="mt-12 flex flex-wrap justify-center gap-3">
        <Button variant="outline" size="sm" className="rounded-full">
          Check symptoms
        </Button>
        <Button variant="outline" size="sm" className="rounded-full">
          Medication questions
        </Button>
        <Button variant="outline" size="sm" className="rounded-full">
          General health advice
        </Button>
        <Button variant="outline" size="sm" className="rounded-full">
          Emergency guidance
        </Button>
      </div>
    </div>
  )
}

function SimpleVoiceAssistant(props: {
  onStateChange: (state: AgentState) => void;
  onTranscription?: (text: string, role: 'user' | 'assistant') => void;
  onLiveKitTranscription?: (text: string, isFinal: boolean, isUser: boolean, participantIdentity?: string, attributes?: Record<string, string>) => void;
}) {
  const { state, audioTrack } = useVoiceAssistant();
  const localParticipant = useLocalParticipant();
  const remoteParticipants = useRemoteParticipants();
  const room = useRoomContext();

  const [isUserSpeaking, setIsUserSpeaking] = useState(false);
  const [isAISpeaking, setIsAISpeaking] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    console.log('🔵 [DEBUG] Voice assistant state changed:', state)
    props.onStateChange(state);
  }, [props, state]);

  useEffect(() => {
    console.log('🔵 [DEBUG] Voice assistant audio track:', audioTrack ? 'Available' : 'Not available')
  }, [audioTrack]);

  // Monitor local participant (user) speaking state
  useEffect(() => {
    if (!localParticipant.localParticipant) return;

    const participant = localParticipant.localParticipant;

    const updateUserState = () => {
      // Check if microphone is muted
      const micTrack = participant.getTrackPublication(Track.Source.Microphone);
      const micMuted = !micTrack?.isEnabled;
      setIsMuted(micMuted);

      // Check if user is speaking
      if (micTrack?.isEnabled) {
        setIsUserSpeaking(participant.isSpeaking || false);
      } else {
        setIsUserSpeaking(false);
      }
    };

    // Initial state
    updateUserState();

    // Listen for track changes
    participant.on('trackMuted', updateUserState);
    participant.on('trackUnmuted', updateUserState);
    participant.on('isSpeakingChanged', updateUserState);

    return () => {
      participant.off('trackMuted', updateUserState);
      participant.off('trackUnmuted', updateUserState);
      participant.off('isSpeakingChanged', updateUserState);
    };
  }, [localParticipant.localParticipant]);

  // Monitor remote participants (AI) speaking state
  useEffect(() => {
    const updateAIState = () => {
      let aiSpeaking = false;

      remoteParticipants.forEach(participant => {
        const audioTrack = participant.getTrackPublication(Track.Source.Microphone);
        if (audioTrack?.isEnabled && participant.isSpeaking) {
          aiSpeaking = true;
        }
      });

      setIsAISpeaking(aiSpeaking);
    };

    // Initial state
    updateAIState();

    // Listen for speaking changes on all remote participants
    const cleanupFunctions: (() => void)[] = [];

    remoteParticipants.forEach(participant => {
      const handleSpeakingChange = () => updateAIState();
      participant.on('isSpeakingChanged', handleSpeakingChange);
      participant.on('trackMuted', handleSpeakingChange);
      participant.on('trackUnmuted', handleSpeakingChange);

      cleanupFunctions.push(() => {
        participant.off('isSpeakingChanged', handleSpeakingChange);
        participant.off('trackMuted', handleSpeakingChange);
        participant.off('trackUnmuted', handleSpeakingChange);
      });
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [remoteParticipants]);

  // Enhanced LiveKit transcription handling
  useEffect(() => {
    console.log('🎤 [TRANSCRIPTION] Setting up LiveKit transcription handlers')
    console.log('🎤 [TRANSCRIPTION] Room available:', room ? 'Yes' : 'No')

    if (!room) {
      console.log('⚠️ [TRANSCRIPTION] No room context available, skipping transcription setup')
      return
    }

    const handleConnected = () => {
      console.log('🎤 [TRANSCRIPTION] Room connected, registering text stream handler')

      // Register transcription handler when connected
      room.registerTextStreamHandler('lk.transcription', async (reader, participantInfo) => {
        try {
          console.log('🎤 [TRANSCRIPTION] Text stream handler triggered')
          console.log('🎤 [TRANSCRIPTION] Participant info:', {
            identity: participantInfo.identity,
            name: participantInfo.name,
            metadata: participantInfo.metadata
          })

          const message = await reader.readAll()
          const attributes = reader.info.attributes
          const isTranscription = attributes && attributes['lk.transcribed_track_id']

          console.log('🎤 [TRANSCRIPTION] Raw text stream data:', {
            message,
            attributes,
            isTranscription,
            participantIdentity: participantInfo.identity,
            localIdentity: room.localParticipant.identity,
            messageLength: message?.length || 0
          })

          if (isTranscription && message) {
            console.log('✅ [TRANSCRIPTION] Valid transcription received from:', participantInfo.identity)

            // Determine if this is from the user or the assistant
            const isUser = participantInfo.identity === room.localParticipant.identity
            const isFinal = attributes && attributes['lk.is_final'] === 'true'

            console.log('🎤 [TRANSCRIPTION] Transcription details:', {
              text: message,
              isUser,
              isFinal,
              participantIdentity: participantInfo.identity,
              attributes
            })

            // Call enhanced transcription handler
            if (props.onLiveKitTranscription) {
              console.log('📡 [TRANSCRIPTION] Calling enhanced transcription handler')
              props.onLiveKitTranscription(message, isFinal || false, isUser, participantInfo.identity, attributes)
            }

            // Call legacy transcription handler for final transcriptions
            if ((isFinal || false) && props.onTranscription) {
              const role = isUser ? 'user' : 'assistant'
              console.log('📡 [TRANSCRIPTION] Calling legacy transcription handler:', { role, text: message })
              props.onTranscription(message, role)
            }

            console.log('✅ [TRANSCRIPTION] Transcription processing complete')
          } else {
            console.log('ℹ️ [TRANSCRIPTION] Non-transcription text stream or empty message:', {
              isTranscription,
              hasMessage: !!message,
              messageLength: message?.length || 0
            })
          }
        } catch (error) {
          console.error('❌ [TRANSCRIPTION] Error processing text stream:', error)
          console.error('❌ [TRANSCRIPTION] Error details:', {
            name: error instanceof Error ? error.name : 'Unknown',
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          })
        }
      })

      console.log('✅ [TRANSCRIPTION] Text stream handler registered successfully')
    }

    const handleDisconnected = () => {
      console.log('🔌 [TRANSCRIPTION] Room disconnected, cleaning up transcription handlers')
    }

    // Set up room event listeners
    if (room.state === 'connected') {
      console.log('🎤 [TRANSCRIPTION] Room already connected, setting up handlers immediately')
      handleConnected()
    } else {
      console.log('🎤 [TRANSCRIPTION] Room not connected yet, waiting for connection')
      room.on(RoomEvent.Connected, handleConnected)
    }

    room.on(RoomEvent.Disconnected, handleDisconnected)

    return () => {
      console.log('🧹 [TRANSCRIPTION] Cleaning up transcription handlers')
      room.off(RoomEvent.Connected, handleConnected)
      room.off(RoomEvent.Disconnected, handleDisconnected)

      try {
        room.unregisterTextStreamHandler('lk.transcription')
        console.log('✅ [TRANSCRIPTION] Text stream handler unregistered')
      } catch (error) {
        console.warn('⚠️ [TRANSCRIPTION] Error unregistering text stream handler:', error)
      }
    }
  }, [room, props])

  // Legacy transcription event listener (keeping for backward compatibility)
  useEffect(() => {
    const handleTranscription = (event: any) => {
      console.log('� [TRANSCRIPTION] Legacy transcription event received:', event.detail)
      if (event.detail?.text && event.detail?.isFinal) {
        const role = event.detail.isUser ? 'user' : 'assistant'
        console.log('✅ [TRANSCRIPTION] Legacy final transcription:', { role, text: event.detail.text })
        props.onTranscription?.(event.detail.text, role)
      }
    }

    console.log('� [TRANSCRIPTION] Adding legacy transcription event listener')
    document.addEventListener('lk-transcription', handleTranscription)
    return () => {
      console.log('� [TRANSCRIPTION] Removing legacy transcription event listener')
      document.removeEventListener('lk-transcription', handleTranscription)
    }
  }, [props])

  return (
    <div className="h-[300px] max-w-[90vw] mx-auto flex items-center justify-center">
      <VoiceActivityCircle
        size="xl"
        showLabel={true}
        isUserSpeaking={isUserSpeaking}
        isAISpeaking={isAISpeaking}
        isMuted={isMuted}
        className="transition-all duration-300"
      />
    </div>
  );
}

function ControlBar(props: {
  onConnectButtonClicked: () => void;
  onEndSession: () => void;
  agentState: AgentState;
}) {
  return (
    <div className="relative h-[100px]">
      <AnimatePresence>
        {props.agentState !== "disconnected" &&
          props.agentState !== "connecting" && (
            <motion.div
              initial={{ opacity: 0, top: "10px" }}
              animate={{ opacity: 1, top: 0 }}
              exit={{ opacity: 0, top: "-10px" }}
              transition={{ duration: 0.4, ease: [0.09, 1.04, 0.245, 1.055] }}
              className="flex h-8 absolute left-1/2 -translate-x-1/2 justify-center"
            >
              <VoiceAssistantControlBar controls={{ leave: false }} />
              <DisconnectButton onClick={props.onEndSession}>
                <CloseIcon />
              </DisconnectButton>
            </motion.div>
          )}
      </AnimatePresence>
      {props.agentState === "connecting" && (
        <div className="absolute left-1/2 -translate-x-1/2 text-center">
          <p className="text-sm text-gray-600">Connecting to agent...</p>
        </div>
      )}
    </div>
  );
}
