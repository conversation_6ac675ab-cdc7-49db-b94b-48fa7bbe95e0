import { getAuthHeaders } from '@/lib/auth-utils'

interface UserProfile {
  id: string
  first_name: string
  last_name: string
  email: string
  date_of_birth?: string
  medical_history_id?: string
  preferences?: {
    theme: string
    notifications_enabled: boolean
  }
  created_at: string
  updated_at: string
}

interface UpdateProfileData {
  first_name?: string
  last_name?: string
  date_of_birth?: string
}

interface UserPreferences {
  theme: string
  notifications_enabled: boolean
}

interface UpdatePreferencesData {
  theme?: string
  notifications_enabled?: boolean
}

interface ChangePasswordData {
  current_password: string
  new_password: string
}

interface ApiResponse<T> {
  success?: boolean
  data?: T
  error?: string
  message?: string
}

export class UserProfileAPI {
  private static baseUrl = "https://medbot-backend.fly.dev/api/v1"

  /**
   * Get the current user's profile
   */
  static async getProfile(): Promise<UserProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/profile`, {
        method: "GET",
        headers: getAuthHeaders(),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to fetch profile")
      }

      return data
    } catch (error) {
      console.error("Get profile error:", error)
      throw error instanceof Error ? error : new Error("Failed to fetch profile")
    }
  }

  /**
   * Update the current user's profile
   */
  static async updateProfile(profileData: UpdateProfileData): Promise<UserProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/profile`, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(profileData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to update profile")
      }

      return data
    } catch (error) {
      console.error("Update profile error:", error)
      throw error instanceof Error ? error : new Error("Failed to update profile")
    }
  }

  /**
   * Get the current user's preferences
   */
  static async getPreferences(): Promise<UserPreferences> {
    try {
      const response = await fetch(`${this.baseUrl}/profile/preferences`, {
        method: "GET",
        headers: getAuthHeaders(),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to fetch preferences")
      }

      return data
    } catch (error) {
      console.error("Get preferences error:", error)
      throw error instanceof Error ? error : new Error("Failed to fetch preferences")
    }
  }

  /**
   * Update the current user's preferences
   */
  static async updatePreferences(preferencesData: UpdatePreferencesData): Promise<UserPreferences> {
    try {
      const response = await fetch(`${this.baseUrl}/profile/preferences`, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(preferencesData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to update preferences")
      }

      return data
    } catch (error) {
      console.error("Update preferences error:", error)
      throw error instanceof Error ? error : new Error("Failed to update preferences")
    }
  }

  /**
   * Change user password
   * Note: This endpoint might not be available in the current API
   * This is a placeholder for future implementation
   */
  static async changePassword(passwordData: ChangePasswordData): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/change-password`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(passwordData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to change password")
      }

      return {
        success: true,
        message: data.message || "Password changed successfully"
      }
    } catch (error) {
      console.error("Change password error:", error)
      throw error instanceof Error ? error : new Error("Failed to change password")
    }
  }

  /**
   * Delete user account
   * Note: This is a placeholder for future implementation
   */
  static async deleteAccount(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/profile`, {
        method: "DELETE",
        headers: getAuthHeaders(),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to delete account")
      }

      return {
        success: true,
        message: data.message || "Account deleted successfully"
      }
    } catch (error) {
      console.error("Delete account error:", error)
      throw error instanceof Error ? error : new Error("Failed to delete account")
    }
  }

  /**
   * Upload profile picture
   * Note: This is a placeholder for future implementation
   */
  static async uploadProfilePicture(file: File): Promise<{ url: string }> {
    try {
      const formData = new FormData()
      formData.append('profile_picture', file)

      const response = await fetch(`${this.baseUrl}/profile/picture`, {
        method: "POST",
        headers: {
          ...getAuthHeaders(),
          // Remove Content-Type to let browser set it with boundary for FormData
          'Content-Type': undefined as any,
        },
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to upload profile picture")
      }

      return data
    } catch (error) {
      console.error("Upload profile picture error:", error)
      throw error instanceof Error ? error : new Error("Failed to upload profile picture")
    }
  }
}
