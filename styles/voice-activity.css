/* Voice Activity Circle Animations */

@keyframes voice-pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes voice-ring {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes voice-glow {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
}

@keyframes muted-fade {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.8;
  }
}

/* Voice Activity States */
.voice-circle-idle {
  animation: none;
}

.voice-circle-speaking {
  animation: voice-pulse 1.5s ease-in-out infinite;
}

.voice-circle-muted {
  animation: muted-fade 2s ease-in-out infinite;
}

.voice-ring-speaking {
  animation: voice-ring 1.5s ease-out infinite;
}

.voice-glow-active {
  animation: voice-glow 1.5s ease-in-out infinite;
}

/* Responsive sizes */
.voice-circle-sm {
  width: 4rem;
  height: 4rem;
}

.voice-circle-md {
  width: 6rem;
  height: 6rem;
}

.voice-circle-lg {
  width: 8rem;
  height: 8rem;
}

.voice-circle-xl {
  width: 12rem;
  height: 12rem;
}

/* Enhanced visual effects */
.voice-circle-gradient {
  background: radial-gradient(circle at 30% 30%, #e0f2fe, #42a5f5, #1565c0);
}

.voice-circle-muted-gradient {
  background: radial-gradient(circle at 30% 30%, #f5f5f5, #9e9e9e, #616161);
}

.voice-circle-user-speaking {
  background: radial-gradient(circle at 30% 30%, #e3f2fd, #2196f3, #0d47a1);
}

.voice-circle-ai-speaking {
  background: radial-gradient(circle at 30% 30%, #e8f5e8, #4caf50, #1b5e20);
}

/* Smooth transitions */
.voice-circle-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .voice-circle-speaking,
  .voice-circle-muted,
  .voice-ring-speaking,
  .voice-glow-active {
    animation: none;
  }
  
  .voice-circle-speaking {
    transform: scale(1.05);
  }
  
  .voice-circle-muted {
    opacity: 0.5;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .voice-circle-gradient {
    background: radial-gradient(circle at 30% 30%, #1e3a8a, #3b82f6, #60a5fa);
  }
  
  .voice-circle-muted-gradient {
    background: radial-gradient(circle at 30% 30%, #374151, #6b7280, #9ca3af);
  }
}
